"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Donut<PERSON><PERSON> } from "@tremor/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
  CompositionDimension,
  getPortfolioComposition,
} from "@/utils/db/portfolio-composition-queries";
import { useQuery } from "@tanstack/react-query";
import { PieChart } from "lucide-react";
import { useState } from "react";

interface PortfolioCompositionCardProps {
  selectedPortfolioIds: string[];
  className?: string;
}

const dimensionOptions = [
  { value: "sector" as CompositionDimension, label: "Sectoare" },
  { value: "industry" as CompositionDimension, label: "Industrii" },
  { value: "country" as CompositionDimension, label: "<PERSON>ări" },
  { value: "currency" as CompositionDimension, label: "Monede" },
  { value: "asset_type" as CompositionDimension, label: "Active" },
];

export function PortfolioCompositionCard({
  selectedPortfolioIds,
  className,
}: PortfolioCompositionCardProps) {
  const [selectedDimension, setSelectedDimension] =
    useState<CompositionDimension>("sector");

  const {
    data: compositionData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "portfolio-composition",
      selectedPortfolioIds,
      selectedDimension,
    ],
    queryFn: () =>
      getPortfolioComposition(selectedPortfolioIds, selectedDimension),
    enabled: selectedPortfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const totalValue =
    compositionData?.reduce((sum, item) => sum + item.value, 0) || 0;

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5 text-portavio-orange" />
            Alocări
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-destructive">
              Eroare la încărcarea datelor de compoziție
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5 text-portavio-orange" />
            Alocări
          </CardTitle>
          <Select
            value={selectedDimension}
            onValueChange={(value) =>
              setSelectedDimension(value as CompositionDimension)
            }
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {dimensionOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <div className="flex items-center justify-center h-64">
              <div className="text-center space-y-4">
                <Skeleton className="h-32 w-32 rounded-full mx-auto" />
                <Skeleton className="h-4 w-48 mx-auto" />
              </div>
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ) : !compositionData || compositionData.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center space-y-2">
              <PieChart className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground">
                Nu există date pentru portofoliile selectate
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Center content with total value */}
            <div className="relative">
              <DonutChart
                data={compositionData}
                category="name"
                index="percentage"
                valueFormatter={formatPercentage}
                showLabel={true}
                showAnimation={true}
                className="h-80"
              />

              {/* Center text overlay */}
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    Total Net Worth
                  </p>
                  <p className="text-2xl font-bold text-portavio-blue">
                    €
                    {totalValue.toLocaleString("ro-RO", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </p>
                </div>
              </div>
            </div>

            {/* Summary table */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-foreground">
                Detalii{" "}
                {
                  dimensionOptions.find((d) => d.value === selectedDimension)
                    ?.label
                }
              </h4>
              <div className="space-y-1">
                {compositionData.slice(0, 5).map((item, index) => (
                  <div
                    key={item.name}
                    className="flex items-center justify-between text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-sm"
                        style={{
                          backgroundColor: `hsl(${
                            (index * 137.5) % 360
                          }, 70%, 50%)`,
                        }}
                      />
                      <span className="text-foreground">{item.name}</span>
                    </div>
                    <span className="font-medium text-foreground">
                      {formatPercentage(item.percentage)}
                    </span>
                  </div>
                ))}
                {compositionData.length > 5 && (
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>+{compositionData.length - 5} altele</span>
                    <span>
                      {formatPercentage(
                        compositionData
                          .slice(5)
                          .reduce((sum, item) => sum + item.percentage, 0)
                      )}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

import { hasuraQuery } from "./hasura";

// Types for portfolio composition data
export interface CompositionData {
  name: string;
  value: number;
  percentage: number;
}

export type CompositionDimension =
  | "sector"
  | "industry"
  | "country"
  | "currency"
  | "asset_type";

// GraphQL Queries

/**
 * Get portfolio composition data with asset relationships
 */
export const GET_PORTFOLIO_COMPOSITION_DATA = `
  query GetPortfolioCompositionData($portfolioIds: [uuid!]!) {
    ptvuser_transactions(
      where: {
        portfolio_id: {_in: $portfolioIds}
      }
    ) {
      ticker
      quantity
      transaction_type
      price
    }

    ptvuser_asset {
      ticker
      sector_id
      industry_id
      country_id
      currency_id
      asset_type_id
    }

    ptvuser_sector {
      sector_id
      name
    }

    ptvuser_industry {
      industry_id
      name
    }

    ptvuser_country {
      country_id
      name
    }

    ptvuser_currency {
      currency_id
      name
    }

    ptvuser_asset_type {
      asset_type_id
      name
    }
  }
`;

// Utility Functions

/**
 * Calculate net position for each ticker from transactions
 */
function calculateNetPositions(
  transactions: any[]
): Map<string, { quantity: number; avgPrice: number }> {
  const positions = new Map<string, { quantity: number; totalValue: number }>();

  transactions.forEach((transaction) => {
    const { ticker, quantity, transaction_type, price } = transaction;
    const current = positions.get(ticker) || { quantity: 0, totalValue: 0 };

    if (transaction_type === "BUY") {
      current.quantity += quantity;
      current.totalValue += quantity * price;
    } else if (transaction_type === "SELL") {
      current.quantity -= quantity;
      // For sells, we reduce the total value proportionally
      const sellRatio = quantity / (current.quantity + quantity);
      current.totalValue -= current.totalValue * sellRatio;
    }

    positions.set(ticker, current);
  });

  // Convert to final format with average price
  const result = new Map<string, { quantity: number; avgPrice: number }>();
  positions.forEach((position, ticker) => {
    if (position.quantity > 0) {
      result.set(ticker, {
        quantity: position.quantity,
        avgPrice: position.totalValue / position.quantity,
      });
    }
  });

  return result;
}

/**
 * Get portfolio composition data by dimension
 */
export async function getPortfolioComposition(
  portfolioIds: string[],
  dimension: CompositionDimension
): Promise<CompositionData[]> {
  if (portfolioIds.length === 0) {
    return [];
  }

  try {
    const result = await hasuraQuery<{
      ptvuser_transactions: Array<{
        ticker: string;
        quantity: number;
        transaction_type: "BUY" | "SELL";
        price: number;
      }>;
      ptvuser_asset: Array<{
        ticker: string;
        sector_id?: string;
        industry_id?: string;
        country_id?: string;
        currency_id?: string;
        asset_type_id?: string;
      }>;
      ptvuser_sector: Array<{
        sector_id: string;
        name: string;
      }>;
      ptvuser_industry: Array<{
        industry_id: string;
        name: string;
      }>;
      ptvuser_country: Array<{
        country_id: string;
        name: string;
      }>;
      ptvuser_currency: Array<{
        currency_id: string;
        name: string;
      }>;
      ptvuser_asset_type: Array<{
        asset_type_id: string;
        name: string;
      }>;
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = result.ptvuser_transactions;
    const assets = result.ptvuser_asset;

    // Create lookup maps for reference data
    const sectorMap = new Map(
      result.ptvuser_sector.map((s) => [s.sector_id, s.name])
    );
    const industryMap = new Map(
      result.ptvuser_industry.map((i) => [i.industry_id, i.name])
    );
    const countryMap = new Map(
      result.ptvuser_country.map((c) => [c.country_id, c.name])
    );
    const currencyMap = new Map(
      result.ptvuser_currency.map((c) => [c.currency_id, c.name])
    );
    const assetTypeMap = new Map(
      result.ptvuser_asset_type.map((at) => [at.asset_type_id, at.name])
    );
    const assetMap = new Map(assets.map((a) => [a.ticker, a]));

    // Calculate net positions for each ticker
    const netPositions = calculateNetPositions(transactions);

    // Group by dimension and calculate total values
    const dimensionValues = new Map<string, number>();
    let totalPortfolioValue = 0;

    netPositions.forEach((position, ticker) => {
      if (position.quantity <= 0) return;

      const positionValue = position.quantity * position.avgPrice;
      totalPortfolioValue += positionValue;

      // Get asset data
      const asset = assetMap.get(ticker);
      if (!asset) {
        const currentValue = dimensionValues.get("Necunoscut") || 0;
        dimensionValues.set("Necunoscut", currentValue + positionValue);
        return;
      }

      // Get dimension name based on selected dimension
      let dimensionName = "Necunoscut";

      switch (dimension) {
        case "sector":
          dimensionName = asset.sector_id
            ? sectorMap.get(asset.sector_id) || "Necunoscut"
            : "Necunoscut";
          break;
        case "industry":
          dimensionName = asset.industry_id
            ? industryMap.get(asset.industry_id) || "Necunoscut"
            : "Necunoscut";
          break;
        case "country":
          dimensionName = asset.country_id
            ? countryMap.get(asset.country_id) || "Necunoscut"
            : "Necunoscut";
          break;
        case "currency":
          dimensionName = asset.currency_id
            ? currencyMap.get(asset.currency_id) || "Necunoscut"
            : "Necunoscut";
          break;
        case "asset_type":
          const assetTypeName = asset.asset_type_id
            ? assetTypeMap.get(asset.asset_type_id) || "Necunoscut"
            : "Necunoscut";
          // Handle asset_type display name transformation
          dimensionName = assetTypeName === "EQUITY" ? "STOCK" : assetTypeName;
          break;
      }

      const currentValue = dimensionValues.get(dimensionName) || 0;
      dimensionValues.set(dimensionName, currentValue + positionValue);
    });

    // Convert to CompositionData array with percentages
    const compositionData: CompositionData[] = Array.from(
      dimensionValues.entries()
    )
      .map(([name, value]) => ({
        name,
        value,
        percentage:
          totalPortfolioValue > 0 ? (value / totalPortfolioValue) * 100 : 0,
      }))
      .sort((a, b) => b.value - a.value); // Sort by value descending

    return compositionData;
  } catch (error) {
    console.error("Error fetching portfolio composition:", error);
    throw new Error(
      "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }
}

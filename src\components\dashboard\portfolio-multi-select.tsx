"use client";

import { MultiSelect, MultiSelectOption } from "@/components/ui/multi-select";
import { usePortfoliosWithMetrics } from "@/hooks/use-portfolios-query";
import { useEffect, useMemo } from "react";

interface PortfolioMultiSelectProps {
  value: string[];
  onValueChange: (value: string[]) => void;
  className?: string;
}

export function PortfolioMultiSelect({
  value,
  onValueChange,
  className,
}: PortfolioMultiSelectProps) {
  const { data: portfolios, isLoading, error } = usePortfoliosWithMetrics();

  const portfolioOptions: MultiSelectOption[] = useMemo(() => {
    if (!portfolios) return [];
    
    return portfolios.map((portfolio) => ({
      value: portfolio.id,
      label: portfolio.name,
      description: portfolio.description || undefined,
    }));
  }, [portfolios]);

  // Auto-select all portfolios when they're first loaded
  useEffect(() => {
    if (portfolios && portfolios.length > 0 && value.length === 0) {
      onValueChange(portfolios.map((p) => p.id));
    }
  }, [portfolios, value.length, onValueChange]);

  if (error) {
    return (
      <div className={className}>
        <p className="text-sm text-destructive">
          Eroare la încărcarea portofoliilor
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      <label className="text-sm font-medium text-foreground mb-2 block">
        Portofolii
      </label>
      <MultiSelect
        options={portfolioOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={
          isLoading
            ? "Se încarcă portofoliile..."
            : "Selectează portofoliile..."
        }
        emptyMessage="Nu s-au găsit portofolii."
        disabled={isLoading}
        selectAllLabel="Selectează toate"
        clearAllLabel="Șterge toate"
        showSelectAll={true}
        showClearAll={true}
        maxDisplayItems={2}
      />
    </div>
  );
}

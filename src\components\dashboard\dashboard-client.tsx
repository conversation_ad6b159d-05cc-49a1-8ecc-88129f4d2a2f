"use client";

import { PortfolioCompositionCard } from "@/components/dashboard/portfolio-composition-card";
import { PortfolioMultiSelect } from "@/components/dashboard/portfolio-multi-select";
import { BarChart3 } from "lucide-react";
import { useState } from "react";

export function DashboardClient() {
  const [selectedPortfolioIds, setSelectedPortfolioIds] = useState<string[]>([]);

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between flex-wrap gap-4">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-portavio-blue" />
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                Dashboard
              </h1>
            </div>
            <p className="text-muted-foreground mt-2">
              Urmărește-ți performanța și analizează portofoliile
            </p>
          </div>
        </div>

        {/* Portfolio selector */}
        <div className="w-full sm:max-w-md">
          <PortfolioMultiSelect
            value={selectedPortfolioIds}
            onValueChange={setSelectedPortfolioIds}
          />
        </div>

        {/* Dashboard cards */}
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
          <PortfolioCompositionCard
            selectedPortfolioIds={selectedPortfolioIds}
            className="lg:col-span-2 xl:col-span-1"
          />
          
          {/* Placeholder for future cards */}
          <div className="lg:col-span-2 xl:col-span-2 grid gap-6 grid-cols-1 md:grid-cols-2">
            {/* Future dashboard cards will go here */}
          </div>
        </div>
      </div>
    </div>
  );
}
